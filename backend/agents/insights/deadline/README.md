# Deadline Insights Agent

The Deadline Insights Agent is a specialized long-running agent that provides comprehensive deadline analysis and insights generation for the AiLex legal AI system. It complements the existing interactive deadline agent by offering batch processing, conflict detection, risk assessment, and advanced deadline analytics.

## Overview

This agent is designed to handle complex deadline analysis tasks that require longer processing times and more comprehensive data analysis than the interactive deadline agent. It follows the established LangGraph patterns and integrates seamlessly with the existing AiLex infrastructure.

## Features

- **Conflict Detection**: Identifies overlapping deadlines, scheduling conflicts, and resource conflicts
- **Risk Assessment**: Analyzes deadline risks based on complexity, dependencies, and historical data
- **SOL Tracking**: Integrates with MCP Rules Engine for statute of limitations tracking
- **Batch Processing**: Handles large datasets efficiently with configurable batch sizes
- **Comprehensive Analysis**: Combines all analysis types for complete deadline insights
- **Caching**: Redis-based caching for improved performance
- **Tenant Isolation**: Secure multi-tenant data handling
- **Error Handling**: Robust error handling with graceful degradation

## Architecture

```
backend/agents/insights/deadline/
├── agent.py              # Main DeadlineInsightsAgent class
├── state.py              # State models and data structures  
├── nodes.py              # LangGraph nodes for processing workflow
├── __init__.py           # Module exports
├── README.md             # This file
└── tests/
    ├── __init__.py       # Test module initialization
    ├── test_agent.py     # Agent tests
    └── test_state.py     # State model tests
```

## Usage

### Basic Usage

```python
from backend.agents.insights.deadline import DeadlineInsightsAgent, DeadlineInsightsState

# Create agent
agent = DeadlineInsightsAgent()

# Create initial state
state = DeadlineInsightsState(
    tenant_id="tenant_123",
    matter_ids=["matter_1", "matter_2"],
    analysis_type=AnalysisType.COMPREHENSIVE
)

# Create and execute workflow
graph = agent.create_graph()
result = await graph.ainvoke(state, {"configurable": {"thread_id": "analysis_123"}})
```

### Conflict Detection

```python
from backend.agents.insights.deadline import AnalysisType

state = DeadlineInsightsState(
    tenant_id="tenant_123",
    matter_ids=["matter_1", "matter_2"],
    analysis_type=AnalysisType.CONFLICT_DETECTION
)

result = await graph.ainvoke(state, config)
conflicts = result.analysis.conflicts
```

### Batch Processing

```python
from backend.agents.insights.deadline import BatchProcessingConfig

batch_config = BatchProcessingConfig(
    batch_size=100,
    max_concurrent_batches=5,
    timeout_seconds=600
)

state = DeadlineInsightsState(
    tenant_id="tenant_123",
    matter_ids=list_of_matter_ids,
    analysis_type=AnalysisType.BATCH_ANALYSIS,
    batch_config=batch_config
)

result = await graph.ainvoke(state, config)
```

## State Model

The `DeadlineInsightsState` includes:

```python
class DeadlineInsightsState(BaseLangGraphState):
    # Analysis configuration
    analysis_type: AnalysisType
    batch_config: BatchProcessingConfig
    
    # Input data
    matter_ids: List[str]
    deadline_ids: List[str]
    date_range_start: Optional[datetime]
    date_range_end: Optional[datetime]
    
    # Processing state
    current_batch: int
    total_batches: int
    processed_deadlines: Set[str]
    failed_deadlines: Set[str]
    
    # Analysis results
    analysis: Optional[DeadlineAnalysis]
    conflicts: List[DeadlineConflict]
    
    # Workflow tracking
    initialized: bool
    data_fetched: bool
    analysis_completed: bool
    cleanup_completed: bool
```

## Analysis Types

### AnalysisType.CONFLICT_DETECTION
Identifies conflicts between deadlines including:
- Overlapping court dates
- Resource scheduling conflicts
- Impossible deadline sequences
- Attorney availability conflicts

### AnalysisType.RISK_ASSESSMENT  
Assesses deadline risks based on:
- Deadline complexity and dependencies
- Historical completion rates
- Resource availability
- External factors (holidays, court schedules)

### AnalysisType.SOL_TRACKING
Tracks statute of limitations deadlines using:
- MCP Rules Engine integration
- Jurisdiction-specific rules
- Trigger event tracking
- Automatic deadline calculation

### AnalysisType.BATCH_ANALYSIS
Processes large datasets efficiently with:
- Configurable batch sizes
- Concurrent processing
- Progress tracking
- Error recovery

### AnalysisType.COMPREHENSIVE
Combines all analysis types for complete insights:
- Full conflict detection
- Complete risk assessment  
- SOL tracking integration
- Comprehensive recommendations

## Integration Points

### Database Integration
- Leverages existing deadline schemas in the tenants database
- Uses established data access patterns from the codebase
- Maintains tenant isolation and security

### MCP Rules Engine
- Integrates with the existing MCP Rules Engine for deadline calculations
- Reuses the proven MCP client patterns from the interactive agent
- Supports all jurisdictions and practice areas

### Caching
- Uses Redis for caching analysis results
- Follows the same caching patterns as the interactive deadline agent
- Configurable TTL and cache size limits

## Configuration

The agent respects the following environment variables:

- `DEADLINE_INSIGHTS_ENABLED`: Enable/disable the insights agent (default: true)
- `DEADLINE_BATCH_SIZE`: Maximum number of deadlines to process in one batch (default: 50)
- `DEADLINE_MAX_CONCURRENT_BATCHES`: Maximum concurrent batches (default: 3)
- `DEADLINE_ANALYSIS_CACHE_TTL`: Cache TTL for analysis results in seconds (default: 600)
- `FEATURE_MCP_RULES_ENGINE`: Enable/disable MCP Rules Engine integration (default: true)

## Error Handling

The agent implements comprehensive error handling:

- **Graceful Degradation**: When external services are unavailable
- **Detailed Error Logging**: For debugging and monitoring
- **State Preservation**: For retry scenarios
- **Tenant Isolation**: Errors in one tenant don't affect others
- **Partial Results**: Returns partial analysis when possible

## Performance

### Caching Strategy
- Redis-based caching with configurable TTL
- Cache keys based on tenant, analysis type, and matter IDs
- Automatic cache invalidation and cleanup
- Cache hit rate monitoring

### Batch Processing
- Configurable batch sizes for optimal performance
- Concurrent batch processing with limits
- Progress tracking and monitoring
- Automatic retry for failed batches

### Monitoring
- Processing time tracking
- Cache hit rate metrics
- Error rate monitoring
- Resource usage tracking

## Development Status

This is the foundational implementation (Task 1). The following components are planned for future tasks:

- **Task 2**: Database integration and data fetching
- **Task 3**: Core analysis algorithms (conflict detection, risk assessment)
- **Task 4**: MCP Rules Engine integration for SOL tracking  
- **Task 5**: Testing and optimization

## Testing

The agent includes comprehensive test coverage:

```bash
# Run all tests
pytest backend/agents/insights/deadline/tests/

# Run specific test files
pytest backend/agents/insights/deadline/tests/test_agent.py
pytest backend/agents/insights/deadline/tests/test_state.py

# Run with coverage
pytest --cov=backend.agents.insights.deadline backend/agents/insights/deadline/tests/
```

## Contributing

When contributing to this agent:

1. Follow the established patterns from the supervisor agent
2. Maintain >90% test coverage
3. Update documentation for new features
4. Use TODO comments for functionality to be implemented later
5. Ensure all code follows the existing codebase style and patterns

## Dependencies

- `pydantic`: Data validation and settings management
- `langgraph`: Workflow orchestration (optional, with fallbacks)
- `langchain-core`: Core LangChain functionality (optional)
- Backend shared core: Base agent and state classes
- Redis: Caching (when available)
- MCP Rules Engine: Deadline calculations (when enabled)
