"""
Test module for Deadline Insights Agent

This module contains comprehensive tests for the Deadline Insights Agent,
including unit tests for the agent, state models, and node implementations.

Test Coverage:
- Agent lifecycle (initialize, execute, cleanup)
- State model validation and serialization
- Node implementations and workflows
- Error handling and edge cases
- Integration patterns and fallbacks
- Performance and caching behavior

Usage:
    # Run all tests
    pytest backend/agents/insights/deadline/tests/
    
    # Run with coverage
    pytest --cov=backend.agents.insights.deadline backend/agents/insights/deadline/tests/
    
    # Run specific test files
    pytest backend/agents/insights/deadline/tests/test_agent.py
    pytest backend/agents/insights/deadline/tests/test_state.py
"""

__version__ = "1.0.0"
__author__ = "AiLex Development Team"
