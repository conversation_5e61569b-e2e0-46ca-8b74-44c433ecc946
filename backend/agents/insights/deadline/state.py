"""
Deadline Insights Agent State Models

This module defines the state models for the Deadline Insights Agent following the
unified architecture development guidelines. It includes comprehensive state tracking
for deadline analysis, conflict detection, and batch processing workflows.
"""

from enum import Enum
from typing import Any, Dict, List, Optional, Set
from datetime import datetime, timezone
from pydantic import BaseModel, Field, validator

# Import from the correct state location - use simpler base for now
class BaseLangGraphState(BaseModel):
    """
    Simplified base state for LangGraph agents.

    This provides the essential fields needed for agent state management
    without requiring complex execution contexts.
    """
    # Core identity fields
    tenant_id: Optional[str] = Field(None, description="Tenant ID for isolation")
    user_id: Optional[str] = Field(None, description="User ID for authorization")
    thread_id: Optional[str] = Field(None, description="Thread ID for conversation")

    # Core LangGraph fields
    messages: List[Any] = Field(default_factory=list, description="List of messages")

    class Config:
        extra = "allow"  # Allow additional fields for flexibility


class AnalysisType(str, Enum):
    """Types of deadline analysis that can be performed."""
    CONFLICT_DETECTION = "conflict_detection"
    RISK_ASSESSMENT = "risk_assessment"
    SOL_TRACKING = "sol_tracking"
    BATCH_ANALYSIS = "batch_analysis"
    COMPREHENSIVE = "comprehensive"


class ConflictSeverity(str, Enum):
    """Severity levels for deadline conflicts."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class DeadlineConflict(BaseModel):
    """
    Model representing a deadline conflict.
    
    This model captures conflicts between deadlines, such as overlapping
    court dates, impossible scheduling scenarios, or resource conflicts.
    """
    id: str = Field(..., description="Unique identifier for the conflict")
    deadline_ids: List[str] = Field(..., description="IDs of conflicting deadlines")
    severity: ConflictSeverity = Field(..., description="Severity of the conflict")
    description: str = Field(..., description="Human-readable description of the conflict")
    suggested_resolution: Optional[str] = Field(None, description="Suggested resolution")
    detected_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class DeadlineAnalysis(BaseModel):
    """
    Model representing the results of deadline analysis.
    
    This model captures the comprehensive analysis results including
    conflicts, risks, and recommendations.
    """
    analysis_id: str = Field(..., description="Unique identifier for the analysis")
    analysis_type: AnalysisType = Field(..., description="Type of analysis performed")
    tenant_id: str = Field(..., description="Tenant ID for isolation")
    matter_ids: List[str] = Field(default_factory=list, description="Matter IDs analyzed")
    
    # Analysis results
    total_deadlines: int = Field(0, description="Total number of deadlines analyzed")
    conflicts: List[DeadlineConflict] = Field(default_factory=list, description="Detected conflicts")
    high_risk_deadlines: List[str] = Field(default_factory=list, description="High-risk deadline IDs")
    recommendations: List[str] = Field(default_factory=list, description="Analysis recommendations")
    
    # Metadata
    started_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    completed_at: Optional[datetime] = Field(None, description="Analysis completion time")
    processing_time_ms: Optional[float] = Field(None, description="Processing time in milliseconds")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class BatchProcessingConfig(BaseModel):
    """
    Configuration for batch processing operations.
    
    This model defines the parameters for batch deadline processing,
    including limits, timeouts, and processing preferences.
    """
    batch_size: int = Field(default=50, ge=1, le=1000, description="Number of deadlines per batch")
    max_concurrent_batches: int = Field(default=3, ge=1, le=10, description="Maximum concurrent batches")
    timeout_seconds: int = Field(default=300, ge=30, le=3600, description="Timeout per batch in seconds")
    enable_caching: bool = Field(default=True, description="Enable Redis caching")
    cache_ttl_seconds: int = Field(default=600, ge=60, le=3600, description="Cache TTL in seconds")
    
    @validator('batch_size')
    def validate_batch_size(cls, v):
        """Ensure batch size is reasonable."""
        if v < 1 or v > 1000:
            raise ValueError("Batch size must be between 1 and 1000")
        return v


class DeadlineInsightsState(BaseLangGraphState):
    """State model for Deadline Insights Agent."""
    
    # Analysis configuration
    analysis_type: AnalysisType = Field(default=AnalysisType.COMPREHENSIVE, description="Type of analysis to perform")
    batch_config: BatchProcessingConfig = Field(default_factory=BatchProcessingConfig, description="Batch processing configuration")
    
    # Input data
    matter_ids: List[str] = Field(default_factory=list, description="Matter IDs to analyze")
    deadline_ids: List[str] = Field(default_factory=list, description="Specific deadline IDs to analyze")
    date_range_start: Optional[datetime] = Field(None, description="Start date for analysis range")
    date_range_end: Optional[datetime] = Field(None, description="End date for analysis range")
    
    # Processing state
    current_batch: int = Field(default=0, description="Current batch being processed")
    total_batches: int = Field(default=0, description="Total number of batches")
    processed_deadlines: Set[str] = Field(default_factory=set, description="Set of processed deadline IDs")
    failed_deadlines: Set[str] = Field(default_factory=set, description="Set of failed deadline IDs")
    
    # Analysis results
    analysis: Optional[DeadlineAnalysis] = Field(None, description="Analysis results")
    conflicts: List[DeadlineConflict] = Field(default_factory=list, description="Detected conflicts")
    
    # Processing status
    status: str = Field("pending", description="Processing status")
    error: Optional[str] = Field(None, description="Error message if failed")
    
    # Workflow tracking
    initialized: bool = Field(False, description="Whether agent is initialized")
    data_fetched: bool = Field(False, description="Whether data has been fetched")
    analysis_completed: bool = Field(False, description="Whether analysis is completed")
    cleanup_completed: bool = Field(False, description="Whether cleanup is completed")
    
    # Performance tracking
    processing_start_time: Optional[datetime] = Field(None, description="Processing start time")
    processing_end_time: Optional[datetime] = Field(None, description="Processing end time")
    cache_hits: int = Field(default=0, description="Number of cache hits")
    cache_misses: int = Field(default=0, description="Number of cache misses")
    
    class Config:
        """Pydantic configuration."""
        extra = "allow"  # Allow additional fields for flexibility
        validate_assignment = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            set: lambda v: list(v)  # Convert sets to lists for JSON serialization
        }


# Function calling schema for LLM integration
ANALYZE_DEADLINES_FN_SCHEMA = {
    "name": "analyze_deadlines",
    "description": "Analyze deadlines for conflicts, risks, and generate insights",
    "parameters": {
        "type": "object",
        "properties": {
            "analysis_type": {
                "type": "string",
                "enum": [e.value for e in AnalysisType],
                "description": "Type of analysis to perform"
            },
            "matter_ids": {
                "type": "array",
                "items": {"type": "string"},
                "description": "Matter IDs to analyze"
            },
            "batch_size": {
                "type": "integer",
                "minimum": 1,
                "maximum": 1000,
                "description": "Number of deadlines to process per batch"
            }
        },
        "required": ["analysis_type"]
    }
}
